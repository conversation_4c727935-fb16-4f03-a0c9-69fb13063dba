// Simple test to verify menu item filtering logic
const { filterMenuItemsByDietaries, filterMenuSectionsByDietaries } = require('./src/utils/menuItemHelper');

// Test data
const testMenuItems = [
  {
    id: 1,
    name: 'Gluten Free Pasta',
    dietaries: ['GF', 'V']
  },
  {
    id: 2,
    name: 'Vegan Burger',
    dietaries: ['VE', 'DF']
  },
  {
    id: 3,
    name: 'Regular Sandwich',
    dietaries: []
  },
  {
    id: 4,
    name: 'Dairy Free Ice Cream',
    dietaries: ['DF', 'V']
  }
];

const testSections = [
  {
    id: 1,
    name: 'Main Dishes',
    menu_items: testMenuItems.slice(0, 2)
  },
  {
    id: 2,
    name: 'Desserts',
    menu_items: testMenuItems.slice(2)
  }
];

// Test filtering by Gluten Free
console.log('Testing Gluten Free filter:');
const glutenFreeItems = filterMenuItemsByDietaries(testMenuItems, ['Gluten Free']);
console.log('Expected: 1 item (Gluten Free Pasta)');
console.log('Actual:', glutenFreeItems.length, 'items:', glutenFreeItems.map(item => item.name));

// Test filtering by Vegan
console.log('\nTesting Vegan filter:');
const veganItems = filterMenuItemsByDietaries(testMenuItems, ['Vegan']);
console.log('Expected: 1 item (Vegan Burger)');
console.log('Actual:', veganItems.length, 'items:', veganItems.map(item => item.name));

// Test filtering by Dairy Free
console.log('\nTesting Dairy Free filter:');
const dairyFreeItems = filterMenuItemsByDietaries(testMenuItems, ['Dairy Free']);
console.log('Expected: 2 items (Vegan Burger, Dairy Free Ice Cream)');
console.log('Actual:', dairyFreeItems.length, 'items:', dairyFreeItems.map(item => item.name));

// Test filtering sections
console.log('\nTesting section filtering with Gluten Free:');
const filteredSections = filterMenuSectionsByDietaries(testSections, ['Gluten Free']);
console.log('Expected: 1 section with 1 item');
console.log('Actual:', filteredSections.length, 'sections');
filteredSections.forEach(section => {
  console.log(`Section "${section.name}": ${section.menu_items.length} items`);
});

// Test no filters
console.log('\nTesting no filters:');
const allItems = filterMenuItemsByDietaries(testMenuItems, []);
console.log('Expected: 4 items (all items)');
console.log('Actual:', allItems.length, 'items');

console.log('\nAll tests completed!');
