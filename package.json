{"name": "yorda<PERSON><PERSON>", "version": "0.1.0", "private": true, "main": "server.js", "engines": {"npm": "Please use yarn for package management"}, "scripts": {"build": "next build", "debug": "NODE_OPTIONS='--inspect' next dev -p 8080", "dev": "next dev -p 8080", "lint": "eslint . --ext .js,.jsx", "prepare": "husky install", "start": "next start -p 8080", "spin": "next build && next start -p 8080", "test": "./node_modules/.bin/cypress open"}, "dependencies": {"@hassanmojab/react-modern-calendar-datepicker": "^3.1.7", "@stripe/react-stripe-js": "^2.1.1", "@stripe/stripe-js": "^1.54.1", "axios": "^1.6.7", "classnames": "^2.2.6", "immer": "^10.0.3", "js-cookie": "^3.0.1", "logrocket": "^3.0.1", "moment": "^2.30.1", "next": "12", "next-redux-wrapper": "^6.0.2", "nextjs-progressbar": "^0.0.7", "optimize-css-assets-webpack-plugin": "^5.0.4", "react": "17.0.2", "react-datepicker": "^4.12.0", "react-debounce-input": "^3.3.0", "react-dom": "17.0.1", "react-flatpickr": "^3.10.13", "react-google-recaptcha": "^2.1.0", "react-inlinesvg": "^3.0.2", "react-places-autocomplete": "^7.3.0", "react-redux": "^7.2.2", "react-responsive-modal": "5.2.6", "react-select": "^5.8.0", "react-toastify": "9.0.3", "redux": "^4.0.4", "redux-devtools-extension": "^2.13.8", "redux-thunk": "^2.3.0", "sass": "^1.43.4", "slugify": "^1.4.6", "stripe": "^12.12.0", "zustand": "^4.3.8"}, "devDependencies": {"babel-eslint": "^10.1.0", "cypress": "^5.5.0", "eslint": "^7.12.1", "eslint-config-airbnb": "^18.2.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "file-loader": "^6.2.0", "husky": "^8.0.1", "prettier": "^2.1.2", "url-loader": "^4.1.1"}}