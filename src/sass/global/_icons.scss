/*=============================================>>>>>
= Icon Helper (for use with IcoMoon or other icon fonts) =
===============================================>>>>>*/

$icons: (
  'about-us-white',
  'about-us',
  'add',
  'after-hours',
  'alarm',
  'apple-white',
  'apple',
  'arrow-left',
  'arrow-right',
  'bill',
  'bin',
  'bin-dark',
  'bread-white',
  'bread',
  'buffet',
  'calendar',
  'calendar-filled',
  'cancel',
  'cancel-black',
  'card',
  'catering',
  'checkmark',
  'chevron-down',
  'chevron-down-primary',
  'chevron-down-white',
  'chevron-left',
  'chevron-right',
  'chevron-up',
  'chocolate-bar-white',
  'chocolate-bar-white',
  'chocolate-bar',
  'cleaning-white',
  'cleaning',
  'clock',
  'close',
  'coffee-white',
  'coffee',
  'company',
  'company-filled',
  'contact-us-white',
  'contact-us',
  'copy',
  'copy-filled',
  'cuisine',
  'event-white',
  'date',
  'dietary',
  'diverse',
  'dock',
  'dollar',
  'dollars',
  'dots-white',
  'dots',
  'eco-friendly',
  'email',
  'event',
  'expand-arrow-white',
  'expand-arrow',
  'filters',
  'gluten',
  'hand-right-white',
  'hand-right',
  'heart-empty',
  'heart-full',
  'indigenous',
  'login',
  'logout',
  'marker',
  'marker-white',
  'marker-filled',
  'milk-white',
  'milk',
  'multi-service',
  'next-day',
  'occasion',
  'one-off',
  'order',
  'pencil',
  'plus-white',
  'plus',
  'phone',
  'phone-filled',
  'restaurant-white',
  'restaurant',
  'recurring',
  'register',
  'search',
  'shopping-basket-white',
  'shopping-basket',
  'shopping-basket-solid',
  'shopcart-white',
  'shopcart-dark',
  'shopping-cart',
  'solo-cup-white',
  'solo-cup',
  'sort',
  'smiley',
  'snacks',
  'survey',
  'teacup-white',
  'teacup',
  'team-white',
  'team',
  'teapot-white',
  'teapot',
  'technical-support-white',
  'technical-support',
  'three-dots',
  'toaster-white',
  'toaster',
  'truck',
  'truck-filled',
  'twitter',
  'upload',
  'upload-white',
  'user',
  'user-grey',
  'user-filled',
  'vegan',
  'vegetarian',
  'why-yordar-white',
  'why-yordar',
  'wine-glass-white',
  'wine-glass',
  'working-hours',
);

.icon {
  &:before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background: no-repeat center;
    background-size: contain;
    vertical-align: sub;
  }
  &-large::before {
    width: 20px;
    height: 20px;
  }
  &-extra-large::before {
    min-width: 26px;
    min-height: 26px;
  }
  &-right-spacer::before {
    margin-right: 8px;
  }
  &-left-spacer::before {
    margin-left: 8px;
  }
  @each $name in $icons {
    &.icon-#{$name} {
      &:before {
        background-image: url('../../images/icons/#{$name}.svg');
      }
    }
  }
  @each $name in $icons {
    &.icon-lt-#{$name} {
      @include media-down(large-tablet) {
        &:before {
          background-image: url('../../images/icons/#{$name}.svg');
        }
      }
    }
  }
}

.beating-heart {
  animation: beating 1s infinite;
}
