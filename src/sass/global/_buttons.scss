$buttons: (
  white: $white,
  black: $black,
  primary: $primary,
  secondary: $secondary,
  tertiary: $tertiary,
  highlight: $highlight,
  grey: #e8e8e8,
);

.button {
  @include heading-font();
  background: $primary;
  color: $white;
  border: 1px solid $primary;
  border-radius: 3px;
  padding: 0.5em 1.5em;
  font-size: 14px;
  font-weight: $font-weight-bold;
  text-transform: uppercase;
  display: inline-block;
  text-align: center;

  &.small {
    font-size: 14px;
  }

  &.tiny {
    @include body-font;
    text-transform: none;
    font-size: 13px;
  }

  &.center {
    display: block;
    margin: auto;
  }

  &.compact {
    padding: 0 0.6rem;
    line-height: 30px;
  }

  &.disabled {
    border: none;
    background: #e2e5e5;
    &:hover {
      cursor: not-allowed;
      background: #e2e5e5;
    }
  }

  &:hover {
    background: darken($primary, 5%);
    border-color: darken($primary, 5%);
    text-decoration: none;
    cursor: pointer;
  }

  @each $name, $color in $buttons {
    &.#{$name} {
      background: $color;
      border-color: $color;
      @if $name == black {
        color: #fff;
      }
      @if $name == white {
        color: black;
        border: 1px solid black;
      }
      @if $name == grey {
        color: black;
        border: 1px solid black;
      }


      &:hover {
        background: darken($color, 5%);
        border-color: darken($color, 5%);
      }

      @if $name == white {
        &:hover {
          background: black;
          border: none;
          color: white;
        }
      }

      &.outline {
        background: transparent;
        color: $color;
        &:hover {
          background: $color;
          color: white;
        }
      }
      &.contrast {
        &:hover {
          background: darken($color, 100%);
          color: white;
        }
      }
    }
  }
}
