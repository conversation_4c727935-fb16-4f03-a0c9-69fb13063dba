import { create } from 'zustand';
import { produce } from 'immer';

import yordar from 'api/yordar';
import {
  CLEAR_CART_ENDPOINT,
  DYNAMIC_LOCATION_ENDPOINT,
  DYNAMIC_ORDER_ENDPOINT,
  DYNAMIC_UPDATE_ORDER_LINE_ENDPOINT,
  ORDER_LINES_ENDPOINT,
  RECURRING_ORDER_ENDPOINT,
} from 'api/endpoints';

const initialState = {
  activeLocationID: null,
  activeOrderID: null,
  orders: {},
  orderSuppliers: {},
  order: {},
  fetchedSession: false,
  mealUUID: null,
  mealPlanAdminNotes: '',
};

const useDocketStore = create((set, get) => ({
  ...initialState,
  addOrderLineToCart: async (orderLines, teamOrderAttendeeID) => {
    const { activeLocationID, activeOrderID, mealUUID, orders, setupOrder } = get();
    let teamOrderLines;
    if (teamOrderAttendeeID) {
      teamOrderLines = orderLines.map((orderline) => ({ ...orderline, attendee_id: teamOrderAttendeeID }));
    }
    try {
      const { data } = await yordar(ORDER_LINES_ENDPOINT, {
        method: 'post',
        data: {
          order_id: activeOrderID,
          order_lines: teamOrderAttendeeID ? teamOrderLines : orderLines,
          ...(!['add-location', 'no-location'].includes(activeLocationID) && { location_id: activeLocationID }),
          ...(mealUUID && { mealUUID }),
        },
        withCredentials: true,
      });
      const noLocations = !orders[data.order.id]?.locations?.[data.location.id];
      const noSuppliers = !orders[data.order.id]?.locations?.[data.location.id]?.suppliers?.[data.supplier.id];
      if (noLocations || noSuppliers) {
        setupOrder(data);
      }
      set(
        produce((draft) => {
          const updatedOrder = draft.orders[data.order.id];
          updatedOrder.totals = data.order.totals;
          const updatedOrderLines = updatedOrder.locations[data.location.id].suppliers?.[data.supplier.id]?.order_lines;
          data.order_lines.forEach((orderline) => {
            updatedOrderLines[orderline.id] = orderline;
          });
        })
      );
    } catch (err) {
      if (err?.response?.data?.redirect_to) {
        // eslint-disable-next-line
        alert(err.response.data.message);
        window.location = err.response.data.redirect_to;
      } else {
        throw new Error(`Add orderline had the following error: ${err}`);
      }
    }
  },
  updateOrderLine: async ({ orderline, quantity }) => {
    const {
      data: { order_line: responseOrderLine, order: responseOrder },
    } = await yordar(DYNAMIC_UPDATE_ORDER_LINE_ENDPOINT(orderline.id), {
      method: 'PUT',
      withCredentials: true,
      data: {
        id: orderline.id,
        order_id: orderline.order_id,
        location_id: orderline.location_id,
        quantity,
        note: orderline.note,
      },
    });
    set(
      produce((draft) => {
        const updatedOrder = draft.orders[orderline.order_id];
        const orderLineSupplier = updatedOrder.locations[orderline.location_id].suppliers[orderline.supplier_id];
        orderLineSupplier.order_lines[responseOrderLine.id] = responseOrderLine;
        updatedOrder.totals = responseOrder.totals;
        if (draft.order.isWoolworthsOrder) {
          draft.order.checkoutErrors.order = ['Cart Items Updated. Validate again!'];
        }
      })
    );
  },
  deleteOrderLine: async ({ locationID, orderLineID, orderID }) => {
    try {
      const { data } = await yordar(DYNAMIC_UPDATE_ORDER_LINE_ENDPOINT(orderLineID), {
        method: 'delete',
        data: {
          location_id: locationID,
          id: orderLineID,
          order_id: orderID,
        },
        withCredentials: true,
      });
      set(
        produce((draft) => {
          const updatedOrder = draft.orders[data.order.id];
          const updatedLocation = updatedOrder.locations[data.location.id];
          const updatedOrderLines = updatedLocation.suppliers[data.supplier.id].order_lines;
          updatedOrder.totals = data.order.totals;
          delete updatedOrderLines[orderLineID];
          if (!Object.values(updatedOrderLines).length) {
            delete updatedLocation.suppliers[data.supplier.id];
          }
        })
      );
    } catch (err) {
      console.log(`Failed to delete order line: ${err}`);
      throw err;
    }
  },
  setupOrder: (data) => {
    set((state) => ({
      activeOrderID: data.order.id,
      activeLocationID: data.location.id,
      order: { ...state.order, ...data.order },
      orders: {
        ...state.orders,
        [data.order.id]: {
          locations: {
            ...state.orders[data.order.id]?.locations,
            [data.location.id]: {
              id: data.location.id,
              name: data.location.details || 'Your Office',
              suppliers: {
                ...state.orders?.[data.order.id]?.locations?.[data.location.id]?.suppliers,
                [data.supplier.id]: {
                  id: data.supplier.id,
                  name: data.supplier.name,
                  slug: data.supplier.slug,
                  order_lines: {},
                },
              },
            },
          },
          totals: data?.order?.totals || {},
        },
      },
    }));
  },
  setActiveOrderID: (activeOrderID) =>
    set((state) => {
      const activeLocationID = state.orders[activeOrderID]?.locations
        ? Object.values(state.orders[activeOrderID]?.locations)[0].id
        : null;
      return { activeOrderID, activeLocationID };
    }),
  setActiveLocationID: (activeLocationID) => set({ activeLocationID }),
  setLocation: async ({ id = '', name, orderID, method, onlyOneLocation = false }) => {
    const { clearOrder } = get();
    try {
      const requiresCartClear = method === 'delete' && onlyOneLocation;
      if (requiresCartClear) {
        return await clearOrder();
      }
      const { data } = await yordar(DYNAMIC_LOCATION_ENDPOINT(id), {
        method,
        data: {
          ...(name && { location: { details: name } }),
          ...(orderID && { order_id: orderID }),
        },
        withCredentials: true,
      });
      if (method === 'delete') {
        set(
          produce((draft) => {
            const targetOrder = draft.orders[data.order.id];
            if (targetOrder?.locations[id]) {
              draft.activeLocationID = null;
              delete targetOrder.locations[id];
            }
          })
        );
        return;
      }

      set(
        produce((draft) => {
          const targetOrder = draft.orders[data.order_id];
          const targetLocation = targetOrder?.locations[data.id];
          if (!targetOrder) {
            draft.orders[data.order_id] = {
              id: data.order_id,
              locations: { [data.id]: { id: data.id, name: data.details, suppliers: {} } },
            };
            draft.activeLocationID = data.id;
            draft.activeOrderID = data.order_id;
            return;
          }
          draft.activeLocationID = data.id;
          draft.activeOrderID = data.order_id;
          if (!targetLocation) {
            targetOrder.locations[data.id] = { id: data.id, name: data.details, suppliers: {} };
            return;
          }
          targetOrder.locations[data.id].name = data.details;
        })
      );
    } catch (error) {
      console.error('Failed to update location name:', error);
    }
  },
  populateSessionOrder: (order) => {
    if (!order?.orders) {
      set({ fetchedSession: true, mealPlanAdminNotes: order?.mealPlanAdminNotes });
      return;
    }
    const { orders: extractedOrders, order_suppliers: orderSuppliers, mealPlanAdminNotes, ...checkoutOrder } = order;
    const activeOrderID = Object.keys(extractedOrders)[0];
    const activeOrder = extractedOrders[activeOrderID];
    const activeLocationID = activeOrder?.locations ? Object.values(activeOrder.locations)[0].id : null;
    set({
      order: {
        ...checkoutOrder,
      },
      activeOrderID,
      orders: extractedOrders,
      orderSuppliers,
      activeLocationID,
      mealPlanAdminNotes,
      fetchedSession: true,
    });
  },
  populateTeamAttendee: (attendee) => {
    set({ teamAttendee: attendee });
  },
  setTeamAttendeeLevel: (level) => {
    set((state) => ({ teamAttendee: { ...state.teamAttendee, team_order_level_id: level } }));
  },
  setRecurringOrder: async ({ formFields, mealUUID }) => {
    const { frequency, recurringDays, copyAll, skipPublic } = formFields;
    const { data } = await yordar(RECURRING_ORDER_ENDPOINT, {
      method: 'post',
      withCredentials: true,
      data: {
        repeat: {
          frequency,
          recurrent_days: recurringDays,
          copy_all: copyAll,
          skip: skipPublic,
        },
        ...(mealUUID && { mealUUID }),
      },
    });
    const { orders, ...orderData } = data;
    set({
      ...initialState,
      order: { ...orderData },
      activeOrderID: data.recurrentOrderDays[0].id,
      activeLocationID:
        !!data.orders[data.recurrentOrderDays[0].id]?.locations &&
        Object.values(data.orders[data.recurrentOrderDays[0].id].locations)[0].id,
      orders,
    });
  },
  setWoolworthsOrderFromModal: async (deliveryDetails) => {
    const { activeOrderID } = get();
    const { deliveryLevel, deliveryAddress, deliveryDate, deliveryInstructions } = deliveryDetails;
    const { data } = await yordar(DYNAMIC_ORDER_ENDPOINT(activeOrderID), {
      method: activeOrderID ? 'put' : 'post',
      withCredentials: true,
      data: {
        order: {
          delivery_address_level: deliveryLevel,
          delivery_address: deliveryAddress.street,
          delivery_at: deliveryDate,
          delivery_instruction: deliveryInstructions,
          is_woolworths_order: true,
        },
        suburb: deliveryAddress.suburb,
        state: deliveryAddress.state,
        postcode: deliveryAddress.postcode,
      },
    });

    set((state) => ({
      activeOrderID: data.order.id,
      order: { ...state.order, ...data.order, isWoolworthsOrder: data.is_woolworths_order },
    }));
  },
  clearOrder: async () => {
    try {
      await yordar(CLEAR_CART_ENDPOINT, {
        method: 'get',
        withCredentials: true,
      });
      // fetchedSession true so cart count from initial load json not used
      set({ ...initialState, fetchedSession: true });
    } catch (err) {
      throw new Error(`Error clearing cart ${err}`);
    }
  },
  setMealUUID: (mealUUID) => set({ mealUUID }),
}));

export default useDocketStore;
