const allServingsSamePrice = (servings) => servings.every((serving) => serving.price === servings[0].price);

export const cardPriceDisplayValue = (price, servings) => {
  const displayPrice = servings?.length ? `$${servings[0].price}` : `$${price}`;
  if (!servings?.length || allServingsSamePrice(servings)) {
    return displayPrice;
  }
  return priceRange(servings);
};

export const truncateText = (description, truncateAt) => {
  if (description?.length > truncateAt) return `${description.slice(0, truncateAt)}...`;
  return description;
};

const priceRange = (servings) => {
  const prices = servings.map((serving) => serving.price);
  const minPrice = Math.min.apply(null, prices).toFixed(2);
  const maxPrice = Math.max.apply(null, prices).toFixed(2);
  return `$${minPrice}-$${maxPrice}`;
};

export const sectionHasNoSearchedItems = (menuItems, searchedMenuItem) =>
  searchedMenuItem && !menuItems.some((item) => item.name.toLowerCase().includes(searchedMenuItem.toLowerCase()));
