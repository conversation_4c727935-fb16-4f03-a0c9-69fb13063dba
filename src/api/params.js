export const supplierShowParams = ['budget', 'selected_menu_sections[]', 'team_order_menu'];
export const supplierIndexParams = ['category_group', 'mealUUID', 'suburb', 'state', 'wants_filter_data'];
export const sessionOrderParams = ['mealUUID'];

const buildQueryParamString = (queryParams) => {
  if (!Object.keys(queryParams).length) return '';
  return `?${Object.entries(queryParams)
    .map(([key, value]) => {
      if (Array.isArray(value)) {
        return value.map((val) => `${key}=${val}`).join('&');
      }
      return `${key}=${value}`;
    })
    .join('&')}`;
};

export const getQueryParams = ({ approvedParams, query }) => {
  const sanitizedParams = {};
  approvedParams.forEach((param) => {
    if (query[param]) {
      sanitizedParams[param] = query[param];
    }
  });
  return buildQueryParamString(sanitizedParams);
};
