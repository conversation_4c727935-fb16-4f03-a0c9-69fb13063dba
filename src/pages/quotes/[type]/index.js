import { useRouter } from 'next/router';
import { QuoteImageSection, QuoteDetails } from 'components/Quotes';
import { Layout } from 'components/Common';
import { cateringQuoteSEO, eventQuoteSEO, snacksQuoteSEO } from 'utils/seo';
import Error404 from 'pages/404';

const quoteSEO = {
  catering: cateringQuoteSEO,
  snacks: snacksQuoteSEO,
  event: eventQuoteSEO,
};

const Quotes = ({ user }) => {
  // Get the type from the route
  const router = useRouter();
  const { type } = router.query;
  const allowedQuotes = ['catering', 'snacks', 'event'];
  if (!allowedQuotes.includes(type)) {
    return <Error404 />;
  }
  const seo = quoteSEO[type];

  const forDash = user?.type === 'customer';

  return (
    <Layout seo={seo} forDash={forDash} customClass={forDash ? 'quotes-dash' : ''} type="quotes-dash">
      <div className="quote-container">
        <div className="auth-container" style={{ padding: '' }}>
          <div className="auth-card">
            <QuoteImageSection type={type} />
            <QuoteDetails type={type} />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Quotes;
