import { useEffect, useState } from 'react';
import { Layout } from 'components/Common';
import { CheckoutDocket, CheckoutForm, CheckoutOrder, OrderStep } from 'components/Checkout';
import yordar from 'api/yordar';

import { CHECKOUT_DETAILS_ENDPOINT, CHECKOUT_ORDER_ENDPOINT } from 'api/endpoints';
import useCheckoutStore from 'store/useCheckoutStore';
import { shallow } from 'zustand/shallow';

const Checkout = ({ details, order, user }) => {
  const { setOrder, setCheckout, orderValidated } = useCheckoutStore(
    (state) => ({
      setOrder: state.setOrder,
      setCheckout: state.setCheckout,
      orderValidated: state.orderValidated,
    }),
    shallow
  );

  const [checkoutPanel, setCheckoutPanel] = useState('order');

  useEffect(() => {
    setOrder(order);
    setCheckout(details);
  }, []);

  const forDash = user?.type === 'customer';
  const isOrderPanel = checkoutPanel === 'order';
  const isMealPlan = !!order?.mealPlan?.uuid;

  return (
    <Layout
      seo={{ title: 'Yordar Checkout', description: 'Checkout With Your Order' }}
      bodyBackgroundColor="#f9f9f9"
      forDash={forDash}
      customClass={forDash ? 'checkout-dash' : ''}
      type="checkout-dash"
      isMealPlan={isMealPlan}
      hideFooter
    >
      <div className="checkout">
        <div className="checkout-container">
          <div>
            {!isMealPlan && (
              <OrderStep
                isOrderPanel={isOrderPanel}
                orderValidated={orderValidated}
                setCheckoutPanel={setCheckoutPanel}
              />
            )}
            {isOrderPanel ? <CheckoutOrder /> : <CheckoutForm />}
          </div>
          <CheckoutDocket order={order} checkoutPanel={checkoutPanel} setCheckoutPanel={setCheckoutPanel} />
        </div>
      </div>
    </Layout>
  );
};

const getCookieHeader = (cookies) => (cookies ? { Cookie: cookies } : {});

const getCheckoutData = async (path, config) => {
  try {
    const { data } = await yordar.get(path, config);
    return data;
  } catch (error) {
    console.error(`Error fetching data from ${path}: `, error);
    throw error;
  }
};

export const getServerSideProps = async ({ req }) => {
  const cookies = req?.headers?.cookie;
  const apiConfig = {
    withCredentials: true,
    headers: getCookieHeader(cookies),
  };

  try {
    const [details, order] = await Promise.all([
      getCheckoutData(CHECKOUT_DETAILS_ENDPOINT, apiConfig),
      getCheckoutData(CHECKOUT_ORDER_ENDPOINT, apiConfig),
    ]);

    return { props: { details, order } };
  } catch (error) {
    return {
      redirect: {
        destination: `${process.env.NEXT_PUBLIC_YORDAR_APP_URL}/c_profile`,
        permanent: false,
      },
    };
  }
};

export default Checkout;
