import { useEffect, useState } from 'react';
import { ErrorComponent, Layout } from 'components/Common';
import { EditDocket, CheckoutForm, CheckoutOrder, CheckoutValidateButton } from 'components/Checkout';
import yordar from 'api/yordar';

import { DYNAMIC_EDIT_DETAILS_ENDPOINT, DYNAMIC_ORDER_EDIT_ENDPOINT } from 'api/endpoints';
import useCheckoutStore from 'store/useCheckoutStore';
import { shallow } from 'zustand/shallow';

const CheckoutEditPage = (props) => {
  if (props.errors) {
    return (
      <ErrorComponent
        text={props.errors.join('.')}
        redirect={props.redirect}
        redirectText="Back To Home"
        forDash={props.user?.type === 'customer'}
      />
    );
  }
  return <CheckoutEdit {...props} />;
};

const CheckoutEdit = ({ details, order, user }) => {
  const { setOrder, setCheckout } = useCheckoutStore(
    (state) => ({
      setOrder: state.setOrder,
      setCheckout: state.setCheckout,
      orderValidated: state.orderValidated,
    }),
    shallow
  );

  const [checkoutPanel, setCheckoutPanel] = useState('edit');

  useEffect(() => {
    setOrder(order);
    setCheckout(details);
  }, []);

  const isMealPlan = !!order?.mealPlan?.uuid;

  return (
    <Layout
      seo={{ title: `Edit Order - ${order.name}`, description: 'Make Changes to Your Placed Order' }}
      bodyBackgroundColor="#f9f9f9"
      customClass="checkout-dash"
      type="checkout-dash"
      isMealPlan={isMealPlan}
      forDash
      hideFooter
    >
      <div className="checkout">
        <div>
          <div>
            <h3 style={{ textAlign: 'center', marginBottom: '20px' }}>Editing Order - #{order.id}</h3>
            <CheckoutOrder />
            <EditDocket order={order} checkoutPanel={checkoutPanel} setCheckoutPanel={setCheckoutPanel} />
            <CheckoutForm isEditPage />
            <div className="checkout-docket">
              <CheckoutValidateButton checkoutPanel={checkoutPanel} canQuote={order.status === 'quoted'} isEditPage />
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

const getCookieHeader = (cookies) => (cookies ? { Cookie: cookies } : {});

const getCheckoutData = async (path, config) => {
  try {
    const { data } = await yordar.get(path, config);
    return data;
  } catch (error) {
    console.error(`Error fetching data from ${path}: `, error);
    throw error;
  }
};

export const getServerSideProps = async ({ req, params, query }) => {
  const { id } = params;
  const { finaliseQuote } = query;
  const cookies = req?.headers?.cookie;
  const apiConfig = {
    withCredentials: true,
    headers: getCookieHeader(cookies),
  };

  try {
    // const [order, details] = await Promise.all([
    //   getCheckoutData(DYNAMIC_ORDER_EDIT_ENDPOINT(id), apiConfig),
    //   getCheckoutData(DYNAMIC_EDIT_DETAILS_ENDPOINT(id), apiConfig),
    // ]);
    // sequential request to catch errors
    const order = await getCheckoutData(DYNAMIC_ORDER_EDIT_ENDPOINT(id, finaliseQuote), apiConfig);
    const details = await getCheckoutData(DYNAMIC_EDIT_DETAILS_ENDPOINT(id), apiConfig);

    return { props: { details, order } };
  } catch (error) {
    if (error.response.status === 403) {
      const loginRedirectUrl = error?.response.data?.login_redirect_url;
      if (loginRedirectUrl) {
        return {
          redirect: {
            destination: loginRedirectUrl,
            permanent: false,
          },
        };
      }
      return {
        props: {
          errors: ['You do not have access to this page'],
        },
      };
    }
    return {
      props: {
        ...(error?.response?.data?.errors && { errors: error.response.data.errors }),
        ...(error.response.data?.redirect_url && { redirect: error.response.data?.redirect_url }),
      },
    };
  }
};

export default CheckoutEditPage;
