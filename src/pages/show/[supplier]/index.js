import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { wrapper } from 'utils/store';

import { fetchSupplier } from 'actions';
import useDocketStore from 'store/useDocketStore';
import { Layout } from 'components/Common';
import { Docket, Menu, SupplierBanner } from 'components/SupplierShow';
import { supplierShowSEO } from 'utils/seo';
import { fetchSessionOrder } from 'utils/api';
import useDeliveryDateStore from 'store/useDeliveryDateStore';

const SupplierShow = ({ listing, user, zustandCart }) => {
  const { title, description } = supplierShowSEO({ name: listing.name, description: listing.description });
  const router = useRouter();
  const {
    query: { menu_item_id: menuItemID, mealUUID },
  } = router;

  const { populateSessionOrder, setMealUUID } = useDocketStore((state) => ({
    populateSessionOrder: state.populateSessionOrder,
    setMealUUID: state.setMealUUID,
  }));

  const { setDeliveryAt, setTime, setDate, setDateValidated } = useDeliveryDateStore((state) => ({
    setDeliveryAt: state.setDeliveryAt,
    setTime: state.setTime,
    setDate: state.setDate,
    setOpenDeliveryModal: state.setOpenDeliveryModal,
    setDateValidated: state.setDateValidated,
  }));

  let localStorageDate;
  let localStorageTime;

  useEffect(() => {
    setMealUUID(mealUUID);
    populateSessionOrder(zustandCart);
  }, []);

  const forDash = user?.type === 'customer';

  if (typeof window !== 'undefined') {
    localStorageDate = localStorage.getItem('deliveryDate');
    localStorageTime = localStorage.getItem('deliveryTime');
  }

  useEffect(() => {
    if (localStorageDate) setDate(localStorageDate);
    if (localStorageTime) setTime(localStorageTime);
    if (localStorageDate && localStorageTime) {
      setDeliveryAt(`${localStorageTime}, ${localStorageDate}`);
    }
    setDateValidated(false);
  }, []);

  const listingRedux = useSelector((state) => state.suppliers.listing);

  return (
    <Layout
      seo={{
        title,
        description,
      }}
      forDash={forDash}
      customClass={forDash ? 'supplier-show' : ''}
      type="menu-dash"
    >
      <div className={`supplier-show-heading-block ${forDash ? 'for-dash' : ''}`} />
      <div className={`supplier-show-container ${forDash ? 'for-dash' : ''}`}>
        <div className="supplier-show-wrapper">
          <div className="supplier-details-container">
            <SupplierBanner listing={listing} />
            <Menu listing={listingRedux.name ? listingRedux : listing} itemFromSearch={menuItemID} />
          </div>
          <Docket />
        </div>
      </div>
    </Layout>
  );
};

export const getServerSideProps = wrapper.getServerSideProps(async ({ req, store, params, query }) => {
  const { getState, dispatch } = store;
  const cookies = req?.headers?.cookie;
  const host = req?.headers?.host;

  const { supplier: slug } = params;
  try {
    const [sessionOrder] = await Promise.all([
      fetchSessionOrder({ cookies, query }),
      dispatch(fetchSupplier({ slug, cookies, host, query })),
    ]);
    const state = getState();
    const { listing } = state.suppliers;
    return {
      props: {
        params,
        listing,
        zustandCart: sessionOrder,
      },
    };
  } catch (error) {
    console.log(error);
    return {
      notFound: true,
    };
  }
});

export default SupplierShow;
