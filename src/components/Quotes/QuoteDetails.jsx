import useQuoteStore from 'store/useQuoteStore';
import { quoteSteps } from './utils/steps';
import QuoteProceedButton from './QuoteProceedButton';

const QuoteDetails = ({ type }) => {
  const { updateStep, step } = useQuoteStore((state) => ({
    updateStep: state.updateStep,
    step: state.step,
  }));

  function goBack(e) {
    e.preventDefault();
    if (step === 0) {
      window.location = '/quotes';
    } else {
      updateStep(false);
    }
  }

  return (
    <div className="authorization-module quote-content">
      <div>
        {quoteSteps[type][step].button && !quoteSteps[type][step].finished && (
          <a onClick={goBack} className="quote-back-link">
            Back
          </a>
        )}
        <h2 className="quote-title">{quoteSteps[type][step].title}</h2>
        <div>{quoteSteps[type][step].content}</div>
      </div>
      {quoteSteps[type][step].button && (
        <QuoteProceedButton
          text={quoteSteps[type][step].button}
          validateFields={quoteSteps[type][step].validationFields}
          type={type}
        />
      )}
    </div>
  );
};

export default QuoteDetails;
