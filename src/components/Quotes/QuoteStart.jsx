import useQuoteStore from 'store/useQuoteStore';
import QuoteTypeModule from './QuoteTypeModule';

import { quoteDescriptions } from './utils/staticText';
import { cateringImage, snacksImage, eventImage } from './utils/images';

const QuoteStart = () => {
  const { setType } = useQuoteStore((state) => ({
    setType: state.setType,
  }));

  return (
    <>
      <QuoteTypeModule
        img={cateringImage}
        description={quoteDescriptions.catering}
        type="catering"
        onClick={() => setType('catering')}
      />
      <QuoteTypeModule
        img={snacksImage}
        description={quoteDescriptions.snacks}
        type="snacks"
        onClick={() => setType('snacks')}
      />
      <QuoteTypeModule
        img={eventImage}
        description={quoteDescriptions.event}
        type="event"
        onClick={() => setType('event')}
      />
    </>
  );
};

export default QuoteStart;
