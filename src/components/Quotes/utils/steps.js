import { allergyDietaryOptions, eventOptions, quoteCategories } from 'components/Quotes/utils/fields';
import { submissionMessages } from 'components/Quotes/utils/staticText';
import QuoteStart from '../QuoteStart';
import QuoteCategorySelect from '../QuoteCategorySelect';
import CateringDetails from '../CateringDetails';
import DietaryAllergiesForm from '../DietaryAllergiesForm';
import CateringRequirementsForm from '../CateringRequirementsForm';
import CateringContactDetailsForm from '../CateringContactDetailsForm';
import FormSubmitSuccessMessage from '../FormSubmitSuccessMessage';
import SnacksRequirementsForm from '../SnacksRequirementsForm';
import SnacksContactDetailsForm from '../SnacksContactDetailsForm';
import EventRequirementsForm from '../EventRequirementsForm';

// Common Constants
const CONTINUE = 'Continue';
const SUBMIT = 'Submit';
const HOME = 'Home';
const COMMON_VALIDATION_FIELDS = ['fullName', 'phone', 'email', 'company'];
const CATERING_VALIDATION_FIELDS = ['location', 'date', 'time', 'estimatedAttendees', 'budget', 'occasion'];

const cateringSteps = [
  {
    title: 'What type of catering are you after?',
    content: <QuoteCategorySelect type="catering" quoteCategories={quoteCategories} />,
    button: CONTINUE,
    validationFields: 'categories',
  },
  {
    title: 'What are the details for your catering service',
    content: <CateringDetails />,
    button: CONTINUE,
    validationFields: CATERING_VALIDATION_FIELDS,
  },
  {
    title: 'Any dietary preferences or allergy concerns?',
    content: (
      <DietaryAllergiesForm
        potentialAllergies={allergyDietaryOptions.potentialAllergies}
        dietaryPreferences={allergyDietaryOptions.preferences}
      />
    ),
    button: CONTINUE,
  },
  { title: 'Do you have any other requirements?', content: <CateringRequirementsForm />, button: CONTINUE },
  {
    title: 'To finish off, can we ask a little bit about you?',
    content: <CateringContactDetailsForm />,
    button: SUBMIT,
    validationFields: COMMON_VALIDATION_FIELDS,
  },
  {
    title: 'Perfect!',
    content: <FormSubmitSuccessMessage message={submissionMessages.catering} />,
    button: HOME,
    finished: true,
  },
];

const snacksSteps = [
  {
    title: 'What snack and pantry solution are you after?',
    content: <QuoteCategorySelect type="snacks" quoteCategories={quoteCategories} />,
    button: CONTINUE,
    validationFields: 'categories',
  },
  {
    title: "What's your current setup?",
    content: <SnacksRequirementsForm />,
    button: CONTINUE,
  },
  {
    title: 'To finish off, can we ask a little bit about you?',
    content: <SnacksContactDetailsForm />,
    button: SUBMIT,
    validationFields: [...COMMON_VALIDATION_FIELDS, 'officeAddress', 'staffSize'],
  },
  {
    title: 'Perfect!',
    content: <FormSubmitSuccessMessage message={submissionMessages.snacks} />,
    button: HOME,
    finished: true,
  },
];

const eventSteps = [
  {
    title: 'What are the details of your event?',
    content: <CateringDetails />,
    button: CONTINUE,
    validationFields: CATERING_VALIDATION_FIELDS,
  },
  {
    title: 'What do you require for your event?',
    content: (
      <EventRequirementsForm
        eventRequirementsList={eventOptions.requirements}
        eventStylesList={eventOptions.serviceStyles}
        serviceStylesList={eventOptions.serviceStyles}
      />
    ),
    button: CONTINUE,
    validationFields: 'event-requirements',
  },
  {
    title: 'Any dietary preferences or allergy concerns?',
    content: (
      <DietaryAllergiesForm
        potentialAllergies={allergyDietaryOptions.potentialAllergies}
        dietaryPreferences={allergyDietaryOptions.preferences}
      />
    ),
    button: CONTINUE,
  },
  {
    title: 'To finish off can we ask a little about you?',
    content: <CateringContactDetailsForm />,
    button: SUBMIT,
    validationFields: COMMON_VALIDATION_FIELDS,
  },
  {
    title: 'Perfect!',
    content: <FormSubmitSuccessMessage message={submissionMessages.event} />,
    button: HOME,
    finished: true,
  },
];

export const quoteSteps = {
  start: [
    {
      title: 'What do you need a quote for?',
      content: <QuoteStart />,
    },
  ],
  catering: cateringSteps,
  snacks: snacksSteps,
  event: eventSteps,
};
