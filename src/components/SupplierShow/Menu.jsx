import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { MajorDeliveryModal, MajorMenuSection, MenuNav, MenuSections, MenuItemModal } from 'components/SupplierShow';
import useItemFromSupplierIndex from 'hooks/SupplierShow/useItemFromSupplierIndex';
import { DeliveryDateMenuModal, CheckoutModal } from 'components/Checkout';
import useDeliveryDateStore from 'store/useDeliveryDateStore';
import useDocketStore from 'store/useDocketStore';

const Menu = ({ listing, itemFromSearch = null }) => {
  const {
    suppliers: {
      listing: { activeSection },
    },
  } = useSelector((state) => state);

  const { showDateModal, modalIsClosable, setOpenDeliveryModal } = useDeliveryDateStore((state) => ({
    showDateModal: state.showDateModal,
    modalIsClosable: state.modalIsClosable,
    setOpenDeliveryModal: state.setOpenDeliveryModal,
  }));

  const { isWoolworthsOrder, fetchedSession, activeSuppliers, order } = useDocketStore((state) => ({
    isWoolworthsOrder: state.order?.isWoolworthsOrder,
    fetchedSession: state.fetchedSession,
    activeSuppliers: state.orders?.[state.activeOrderID]?.locations?.[state.activeLocationID]?.suppliers,
    order: state.order,
  }));

  const [aisleAndSection, setAisleAndSection] = useState({ activeAisle: 0, activeSection: 0 });
  const [showModal, setShowModal] = useState(false);
  const addingDisabled =
    order?.isRecurrent &&
    activeSuppliers &&
    !!Object.values(activeSuppliers).filter((supplier) => supplier.id !== listing.id).length;

  const isMajorSupplier = listing.slug === 'woolworths';

  useEffect(() => {
    if (fetchedSession) {
      setShowModal(isMajorSupplier && !isWoolworthsOrder);
    }
  }, [isWoolworthsOrder, fetchedSession]);

  useItemFromSupplierIndex(itemFromSearch, listing.section_grouped_menu_items, isMajorSupplier);

  return (
    <>
      <MenuNav
        menuSections={listing.section_grouped_menu_items}
        dualNav={isMajorSupplier}
        aisleAndSection={aisleAndSection}
        setAisleAndSection={setAisleAndSection}
      />

      {isMajorSupplier && (
        <MajorDeliveryModal
          showModal={showModal}
          setShowModal={setShowModal}
          previousDeliveryInstructions={listing?.delivery_instructions}
        />
      )}
      {isMajorSupplier && activeSection && <MajorMenuSection section={activeSection} recentOrders={listing.recent_orders} />}
      {!isMajorSupplier && (
        <MenuSections sections={listing.section_grouped_menu_items} recentOrders={listing.recent_orders} />
      )}
      <MenuItemModal addingDisabled={addingDisabled} supplierID={listing.id} />
      {!isMajorSupplier && (
        <CheckoutModal
          open={showDateModal}
          setModalOpen={modalIsClosable ? setOpenDeliveryModal : null}
          dimensionOptions={{ width: '460px', minHeight: '500px' }}
          noClose={modalIsClosable ? false : () => null}
          showCloseIcon={modalIsClosable}
        >
          <DeliveryDateMenuModal />
        </CheckoutModal>
      )}
    </>
  );
};

export default Menu;
