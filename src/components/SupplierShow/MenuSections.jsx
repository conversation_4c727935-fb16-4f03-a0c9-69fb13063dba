import { useContext, useRef } from 'react';

import { UserContext } from 'context/user';
import useActiveMenuSection from 'hooks/SupplierShow/useActiveMenuSection';
import { MenuItem, OrderAgainMenuSection } from 'components/SupplierShow';
import { useRouter } from 'next/router';

const MenuSections = ({ sections, searchedMenuItem, recentOrders }) => {
  const { user } = useContext(UserContext);
  const {
    query: { mealUUID },
  } = useRouter();
  const menuSectionRefs = useRef(new Array(sections.length).fill(null));

  useActiveMenuSection(menuSectionRefs);

  return (
    <div className="menu-section-wrapper">
      <div className="menu-sections">
        {!!recentOrders?.length && <OrderAgainMenuSection recentOrders={recentOrders} />}

        {sections.map((section, i) => {
          const showMealPlanTag =
            mealUUID &&
            section.categories.some((category) =>
              ['buffets', 'individually-boxed-meals', 'share-meals'].includes(category)
            );

          return (
            <div
              className="menu-section"
              id={`menu-section-${section.id}`}
              key={section.id}
              ref={(ref) => {
                menuSectionRefs.current[i] = ref;
              }}
              data-title={section.name}
            >
              <h2 className="menu-section-title">
                {section.name}
                {showMealPlanTag ? <span className="meal-plan-section-tag">Meal Plan Section</span> : ''}
              </h2>
              <ul className="menu-section-items">
                {section.menu_items.map((item) => (
                  <MenuItem key={item.id} item={item} searchedMenuItem={searchedMenuItem} user={user} />
                ))}
              </ul>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MenuSections;
