import { MenuNav, MenuSections, MenuItemModal } from 'components/SupplierShow';
import useItemFromSupplierIndex from 'hooks/SupplierShow/useItemFromSupplierIndex';
import useMenuItemStore from 'store/useMenuItemStore';

const TeamOrderMenu = ({ listing, itemFromSearch = null }) => {
  const activeMenuItem = useMenuItemStore((state) => state.menuItem);

  const isMajorSupplier = listing.slug === 'woolworths';

  useItemFromSupplierIndex(itemFromSearch, listing.section_grouped_menu_items, isMajorSupplier);

  return (
    <>
      <MenuNav
        menuSections={listing.section_grouped_menu_items}
        dualNav={isMajorSupplier}
        aisleAndSection={false}
        setAisleAndSection={false}
      />
      <MenuSections sections={listing.section_grouped_menu_items} />
      <MenuItemModal item={activeMenuItem} supplierID={listing.id} isTeamOrder />
    </>
  );
};

export default TeamOrderMenu;
