import { memo, useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import yordar from 'api/yordar';

import { DYNAMIC_FAVOURITE_ITEM_ENDPOINT } from 'api/endpoints';
import { FavouriteHeart, Image } from 'components/Common';
import { MenuSectionSkeleton, OrderAgainMenuSection } from 'components/SupplierShow';
import { UserContext } from 'context/user';
import { truncateText } from 'utils/menuItemHelper';
import useModalStore from 'store/useModalStore';
import useMenuItemStore from 'store/useMenuItemStore';
import useSearchResultsStore from 'store/useSearchResultsStore';
import useMajorFavouritesStore from 'store/useMajorFavouritesStore';

const MajorMenuSection = ({ section, searchedMenuItem, recentOrders }) => {
  const { user } = useContext(UserContext);
  const { favourites: favouriteIDs, setMajorFavourites } = useMajorFavouritesStore((state) => ({
    favourites: state.favourites,
    setMajorFavourites: state.setMajorFavourites,
  }));
  const [favourites, setFavourites] = useState(section.menu_items.filter((menuItem) => menuItem.is_favourite));
  const [showFavouriteSection, setShowFavouriteSection] = useState(false);
  const { loadingMenu } = useSelector((state) => state.suppliers.listing);

  const { results, searchValue, showInMenu, setShowInMenu } = useSearchResultsStore((state) => ({
    results: state.results,
    searchValue: state.searchValue,
    showInMenu: state.showInMenu,
    setShowInMenu: state.setShowInMenu,
  }));

  useEffect(() => {
    if (section.name === 'Favourites' && !showInMenu) {
      return setShowFavouriteSection(false);
    }
    if (favouriteIDs.length) {
      setShowFavouriteSection(true);
      if (showInMenu) {
        return setFavourites(results.filter((menuItem) => favouriteIDs.includes(menuItem.id)));
      }
      setFavourites(section.menu_items.filter((menuItem) => favouriteIDs.includes(menuItem.id)));
    } else {
      setShowFavouriteSection(false);
    }
  }, [favouriteIDs]);

  useEffect(() => {
    if (showInMenu) {
      setMajorFavourites(results.filter((menuItem) => menuItem.is_favourite).map((favourite) => favourite.id));
      return;
    }
    setMajorFavourites(section.menu_items.filter((menuItem) => menuItem.is_favourite).map((favourite) => favourite.id));
  }, [section, showInMenu, setMajorFavourites, results]);

  if (loadingMenu) {
    return <MenuSectionSkeleton />;
  }

  return (
    <>
      {showFavouriteSection && !!favourites.length && (
        // Favourite Section
        <div>
          <div className="menu-section-wrapper">
            <div className="menu-sections">
              <div className="menu-section" id={`menu-section-${section.id}`} data-title={section.name}>
                <h2 className="menu-section-title">Favourites</h2>
                <ul className="menu-section-items">
                  {favourites.map((item) => (
                    <MenuItemCard
                      key={item.id}
                      item={item}
                      searchedMenuItem={searchedMenuItem}
                      user={user}
                      favouriteIDs={favouriteIDs}
                    />
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
      {showInMenu && (
        // Search Results
        <div className="menu-section-wrapper">
          <div className="menu-sections">
            <div className="menu-section" id="menu-section-search-results" data-title="Search Results">
              <h2 className="menu-section-title with-clear">
                Search Results For: {searchValue}
                <a className="clear-searched-items" onClick={() => setShowInMenu(false)}>
                  Clear
                </a>
              </h2>
              <ul className="menu-section-items">
                {results.map((item) => (
                  <MenuItemCard
                    key={item.id}
                    item={item}
                    searchedMenuItem={searchedMenuItem}
                    user={user}
                    favouriteIDs={favouriteIDs}
                  />
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
      {!showInMenu && section.name === 'Order Again' && (
        <div className="menu-section-wrapper">
          <div className="menu-sections">
            {!!recentOrders?.length && <OrderAgainMenuSection recentOrders={recentOrders} />}
          </div>
        </div>
      )}
      {!showInMenu && section.name !== 'Order Again' && (
        // Section
        <div className="menu-section-wrapper">
          <div className="menu-sections">
            {!!recentOrders?.length && section.name === 'Favourites' && <OrderAgainMenuSection recentOrders={recentOrders} />}
            <div className="menu-section" id={`menu-section-${section.id}`} data-title={section.name}>
              <h2 className="menu-section-title">{section.name}</h2>
              <ul className="menu-section-items">
                {section.menu_items.map((item) => (
                  <MenuItemCard
                    key={item.id}
                    item={item}
                    searchedMenuItem={searchedMenuItem}
                    user={user}
                    favouriteIDs={favouriteIDs}
                  />
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

const MenuItemCard = memo(function MenuItemCard({ item, searchedMenuItem, user, favouriteIDs }) {
  const {
    id,
    description,
    dietaries,
    image_id: imageId,
    minimum_quantity: minimumQuantity,
    name,
    display_price: displayPrice,
    serving_sizes: servings,
    discount,
    has_promotion: hasPromotion,
  } = item;
  const menuItemNotFoundInSearch = searchedMenuItem && !name.toLowerCase().includes(searchedMenuItem.toLowerCase());
  const setModalOpen = useModalStore((state) => state.setModalOpen);
  const setMenuItem = useMenuItemStore((state) => state.setMenuItem);
  if (menuItemNotFoundInSearch) return null;
  const servingsHaveDiscount = servings?.some((serving) => serving.discount);
  return (
    <li
      className="menu-section-item"
      onClick={() => {
        setModalOpen(true);
        setMenuItem(item);
      }}
      role="presentation"
    >
      <div className="menu-section-item-title-and-add-btn">
        <h4>{truncateText(name, 40)}</h4>
        {user && (
          <FavouriteHeart
            id={id}
            isFavourite={favouriteIDs.includes(id)}
            changeFavouriteStatus={changeFavouriteStatus}
          />
        )}
      </div>
      <div className="menu-section-item-description-and-image">
        <div className="menu-section-item-description">
          <p>{description}</p>
          <div>
            {dietaries?.map((dietary) => (
              <span className={`letter-icon ${dietary}`} key={dietary}>
                {dietary}
              </span>
            ))}
          </div>
        </div>
        {imageId && (
          <Image url={imageId} className="menu-section-item-image" width={100} height={100} transform="fill" />
        )}
      </div>
      <div className="menu-section-item-price-and-quantity">
        {servings?.length ? (
          <>
            <span className={`${servingsHaveDiscount ? 'rate-card-price' : ''}`}>
              {servingsHaveDiscount ? 'Custom Pricing' : displayPrice}
            </span>
          </>
        ) : (
          <>
            <span className={discount ? 'rate-card-price strike' : ''}>{displayPrice}</span>
            {discount && hasPromotion && (
              <span>
                <span className="special-label">SPECIAL</span>
                <span className="special-price">${discount}</span>
              </span>
            )}
            {discount && !hasPromotion && <span>Your Price: ${discount}</span>}
          </>
        )}
        {minimumQuantity > 1 && <span>Min Qty: {minimumQuantity}</span>}
      </div>
    </li>
  );
});

async function changeFavouriteStatus({ e, id, method, setBeatingHeart }) {
  const { updateMajorFavourites } = useMajorFavouritesStore.getState();

  e.stopPropagation();
  setBeatingHeart(true);
  await yordar(DYNAMIC_FAVOURITE_ITEM_ENDPOINT(id), {
    method,
    withCredentials: true,
  });
  setBeatingHeart(false);

  if (method === 'put') {
    updateMajorFavourites({ type: 'add', favourite: id });
  } else {
    updateMajorFavourites({ favourite: id });
  }
}

export default MajorMenuSection;
