import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Image, OutsideAlerter } from 'components/Common';
import useModalStore from 'store/useModalStore';
import useMenuItemStore from 'store/useMenuItemStore';

const SearchMenuItems = ({ forDash }) => {
  const supplier = useSelector((state) => state.suppliers.listing);
  const setModalOpen = useModalStore((state) => state.setModalOpen);
  const setMenuItem = useMenuItemStore((state) => state.setMenuItem);
  const [searchedMenuValue, setSearchedMenuValue] = useState('');
  const [matchedMenuItems, setMatchedMenuItems] = useState(flattenSortMenuItems(supplier?.section_grouped_menu_items));
  const [inputIsFocued, setInputIsFocused] = useState(false);

  useEffect(() => setMatchedMenuItems(flattenSortMenuItems(supplier?.section_grouped_menu_items)), [supplier]);

  function handleMenuItemSearch(e) {
    setSearchedMenuValue(e.target.value);
    setMatchedMenuItems(searchMenuItems(e.target.value, supplier?.section_grouped_menu_items));
  }

  const handleSearchItemClick = (e, menuItem) => {
    e.preventDefault();
    setMenuItem(menuItem);
    setModalOpen(true);
  };

  function searchMenuItems(value, menuSections) {
    const flattenedSortedMenuItems = flattenSortMenuItems(menuSections);
    const menuItemMatches = flattenedSortedMenuItems.filter((item) =>
      item.name.toLowerCase().includes(value.toLowerCase())
    );
    return menuItemMatches;
  }

  function flattenSortMenuItems(menuSections) {
    if (!menuSections) return [];
    const menuItems = menuSections.map((section) => section.menu_items);
    const flattenedMenuItems = [].concat(...menuItems);
    return flattenedMenuItems.sort((a, b) => {
      if (a.name < b.name) return -1;
      if (a.name > b.name) return 1;
      return 0;
    });
  }

  function caseInsensitiveRegex(str) {
    return new RegExp(str, 'gi');
  }

  if (!supplier.name) return null;
  return (
    <div className="search-menu-items-container">
      <OutsideAlerter className={`elastic-search-group active ${forDash ? 'dash' : ''}`}>
        <input
          id="search-menu-items"
          type="text"
          placeholder={`Search ${supplier.name}'s Menu`}
          value={searchedMenuValue}
          onChange={handleMenuItemSearch}
          autoComplete="off"
          onFocus={() => setInputIsFocused(true)}
        />
        {inputIsFocued && (
          <div className="elastic-search-results">
            <>
              <p className="search-result-title">Menu Items ({matchedMenuItems.length})</p>
              {matchedMenuItems.map((menuItem) => (
                <div className="search-result-container">
                  {menuItem.image_id ? (
                    <Image
                      url={menuItem.image_id}
                      width={44}
                      height={44}
                      className="circle-icon"
                      alt={`${menuItem.name}`}
                      quality={3}
                      noContainer
                    />
                  ) : (
                    <span className="circle-icon no-image no-margin">{menuItem.name[0]}</span>
                  )}
                  <div className="search-result-info">
                    <a
                      className="search-result-name"
                      dangerouslySetInnerHTML={{
                        __html: menuItem.name.replace(
                          caseInsensitiveRegex(searchedMenuValue),
                          (str) => `<strong>${str}</strong>`
                        ),
                      }}
                      onClick={(e) => handleSearchItemClick(e, menuItem)}
                    />
                    <p className="search-result-label">{menuItem.display_price}</p>
                  </div>
                </div>
              ))}
            </>
          </div>
        )}
      </OutsideAlerter>
    </div>
  );
};

export default SearchMenuItems;
