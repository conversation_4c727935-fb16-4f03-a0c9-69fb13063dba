import { DocketSupplier } from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';

const QuickCartView = ({ checkoutPanel }) => {
  const { order: checkoutOrder, orders, activeOrderID, orderSuppliers } = useCheckoutStore();

  if (!orders) return null;

  if (!activeOrderID) return null;

  const activeOrder = orders[activeOrderID];
  if (!activeOrder) return null;

  const submmittedOrderID = ['quoted', 'new', 'saved'].includes(checkoutOrder.status) ? checkoutOrder.id : null;

  // Tally object to store the count for each supplier
  const suppliers = {};
  // Iterate through locations
  Object.values(activeOrder.locations).forEach((location) => {
    // Iterate through suppliers
    Object.values(location.suppliers).forEach((supplier) => {
      suppliers[supplier.id] ||= { id: supplier.id, orderLineCount: 0 };
      suppliers[supplier.id].orderLineCount += Object.values(supplier.order_lines).reduce(
        (sum, orderLine) => sum + orderLine.quantity,
        0
      );
    });
  });

  return (
    <div className="checkout-docket-section" style={{ borderBottom: '2px solid #eaeaea', paddingBottom: '18px' }}>
      <div className="section-heading flex">
        {!checkoutOrder?.mealPlan?.name && (
          <h3 style={{ fontFamily: 'Museo Sans', fontSize: '22px', marginBottom: '16px' }}>
            {submmittedOrderID && (
              <a href={checkoutOrder.orderLink}>
                Your Order - <span style={{ color: '#1f9e86' }}>#{submmittedOrderID}</span>
              </a>
            )}
            {!submmittedOrderID && 'Your Order'}
          </h3>
        )}
      </div>
      {Object.values(suppliers).map((supplier, index) => (
        <DocketSupplier
          key={`order-${activeOrder.id}-supplier-${supplier.id}`}
          supplier={supplier}
          index={index}
          checkoutOrder={checkoutOrder}
          activeOrder={activeOrder}
          orderSuppliers={orderSuppliers}
          checkoutPanel={checkoutPanel}
        />
      ))}
    </div>
  );
};

export default QuickCartView;
