import { DocketDays, OrderCoupon, OrderErrors, OrderTotals, QuickCartView, MealPlanDocket } from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';

const EditDocket = ({ checkoutPanel, setCheckoutPanel }) => {
  const {
    orders,
    order: { isRecurrent, mealPlan },
  } = useCheckoutStore();

  const renderRecurringDays = checkoutPanel !== 'order' && Object.keys(orders).length > 1;

  return (
    <div className="checkout-docket no-sticky">
      {renderRecurringDays && <DocketDays />}
      <div className="checkout-docket-info">
        {mealPlan?.uuid && (
          <MealPlanDocket checkoutPanel={checkoutPanel} setCheckoutPanel={setCheckoutPanel} isEditPage />
        )}
        <QuickCartView checkoutPanel={checkoutPanel} />
        {!isRecurrent && <OrderCoupon />}
        <OrderTotals />
        <OrderErrors checkoutPanel={checkoutPanel} />
      </div>
    </div>
  );
};

export default EditDocket;
