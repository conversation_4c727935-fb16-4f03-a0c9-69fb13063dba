import {
  CheckoutSuccessButtons,
  CheckoutValidateButton,
  DocketDays,
  OrderCoupon,
  OrderErrors,
  OrderTotals,
  QuickCartView,
  OrderValidateButton,
  MealPlanDocket,
} from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';

const CheckoutDocket = ({ checkoutPanel, setCheckoutPanel }) => {
  const {
    orders,
    order: { isRecurrent, mealPlan, status },
  } = useCheckoutStore();

  const renderRecurringDays = checkoutPanel !== 'order' && Object.keys(orders).length > 1;

  const moveToDetails = () => setCheckoutPanel('details');
  const moveToOrderSummary = () => setCheckoutPanel('order');

  const checkoutButtons = {
    order: <OrderValidateButton checkoutPanel={checkoutPanel} moveToDetails={moveToDetails} />,
    details: <CheckoutValidateButton moveToOrderSummary={moveToOrderSummary} />,
    success: <CheckoutSuccessButtons />,
    edit: (
      <CheckoutValidateButton
        checkoutPanel={checkoutPanel}
        moveToDetails={moveToDetails}
        canQuote={status === 'quoted'}
        isEditPage
      />
    ),
  };

  const activeButton = checkoutButtons[checkoutPanel];

  return (
    <div className="checkout-docket">
      {renderRecurringDays && <DocketDays />}
      <div className="checkout-docket-info">
        {mealPlan?.uuid && <MealPlanDocket checkoutPanel={checkoutPanel} setCheckoutPanel={setCheckoutPanel} />}
        <QuickCartView checkoutPanel={checkoutPanel} />
        {checkoutPanel !== 'success' && !isRecurrent && <OrderCoupon />}
        <OrderTotals />
        <OrderErrors checkoutPanel={checkoutPanel} moveToOrderSummary={moveToOrderSummary} />
      </div>
      {activeButton}
    </div>
  );
};

export default CheckoutDocket;
