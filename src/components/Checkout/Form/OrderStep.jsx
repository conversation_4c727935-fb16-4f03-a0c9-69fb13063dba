const OrderStep = ({ isOrderPanel, orderValidated, setCheckoutPanel }) => (
  <div className="order-progress">
    <h3 className={isOrderPanel ? 'active' : ''} onClick={() => setCheckoutPanel('order')}>
      1. Order Summary
    </h3>
    <h3
      className={!isOrderPanel ? 'active' : ''}
      style={!orderValidated ? { cursor: 'not-allowed' } : {}}
      onClick={orderValidated ? () => setCheckoutPanel('details') : null}
    >
      {' '}
      2. Order Details
    </h3>
  </div>
);

export default OrderStep;
