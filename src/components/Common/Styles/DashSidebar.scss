.customer-sticky-sidebar {
  top: 0;
  min-width: 250px;
  @include media-down(large-tablet) {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100vw;
    height: 100vh;
    z-index: 998;
  }
  &.mobile-open {
    display: block;
  }
}

.customer-area-sidebar {
  position: sticky;
  top: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: white;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.15);
  overflow-y: scroll;
  height: 100vh;
  padding: 0 24px;
  max-width: 250px;
  &.invert {
    filter: invert(1);
  }
  @include media-down(large-tablet) {
    padding: 0 20px 80px;
    max-width: initial;
  }
  &__image {
    display: flex;
    align-items: center;
    margin: 16px 0;
    img {
      width: 95px;
      height: auto;
    }
  }
  &__title {
    font-size: 14px;
    text-transform: uppercase;
    color: #828D99;
  }
  &__admin-title {
    font-size: 20px;
    font-weight: 800;
    text-transform: uppercase;
    color: $black;
  }
  &__ul {
    display: block;
    margin: 0;
    & > li {
      font-size: rem-calc(14);
      font-weight: 500;
      &.nested-list {
        .customer-sidebar-link {
          display: flex;
          align-items: center;
          &::after {
            content: '';
            display: inline-block;
            background: url('../../../images/icons/chevron-down.svg') 50% no-repeat;
            transform: rotate(-90deg);
            width: 14px;
            height: 14px;
            margin-left: auto;
          }
          &.open::after {
            transform: rotate(0);
          }
        }
        &.active {
          .customer-sidebar-link::after {
            background: url('../../../images/icons/chevron-down-white.svg') 50% no-repeat;
          }
          .customer-sidebar-link::before {
            filter: invert(1);
          }
        }
      }
      .customer-sidebar-link__access::after {
        content: 'ADMIN';
        background: #fb35dc;
        color: white;
        font-size: 10px;
        font-weight: bold;
        padding: 8px 5px;
        margin-left: 8px;
        height: 14px;
        align-items: center;
        border-radius: 4px;
        display: flex;
        align-items: center;
        line-height: 1px;
      }
      & > a, & > span {
        display: flex;
        align-items: center;
        color: #191919;
        font-size: 14px;
        padding: 10px 17px;
        margin: 0 4px 10px;
        border-radius: 4px;
        &:hover {
          background: #E8EDF2;
          text-decoration: none;
        }
        &.shortcut {
          padding: 10px 5px;
        }
      }
      &.active {
        cursor: pointer;
        & > a {
          color: #F4F0EB;
          background: #191919;
          border-top: none;
        }
      }
    }
    .nested {
      padding: 6px 0;
      margin: 0;
      & > li {
        & > a {
          color: #191919;
          opacity: 0.5;
          font-size: 14px;
          transition: background-color 0.25s ease-out, color 0.25s ease-out;
          background: none;
          border-radius: 10px;
          margin: 0 1rem;
          margin-left: 22px;
          &:hover {
            cursor: pointer;
            opacity: 1;
          }
        }
        &.active {
          & > a {
            cursor: default;
            color: #191919;
            opacity: 1;
            background: none;
          }
        }
      }
    }
  }
  &__li {
    transition: background-color 0.25s ease-out, color 0.25s ease-out;
    color: #191919;
    margin: 0 -20px;
    &:hover {
      cursor: pointer;
    }
  }
  &__button {
    border: 1px solid black;
    color: black;
    border-radius: 4px;
    padding: 6px;
    display: inline-block;
    font-size: 14px;
    margin-bottom: 20px;
    &:hover {
      color: black;
    }
    &:last-of-type {
      margin-left: 10px;
    }
    @include media-down(hamburger) {
      display: inline-block;
    }
    @include media-up(hamburger) {
      display: none;
    }
  }
  &__shortcuts {
    margin-bottom: 20px;
  }
}

.orders-list, .team-orders-list, .quotes-list, .with-favourite-heart, .billing-list, .reports-list, .surveys-list, .settings-list, .catering, .snacks, .meal-plans {
  &::before {
    content: '';
    display: inline-block;
    background-size: contain;
    width: 24px;
    height: 24px;
    background-position: center;
    background-repeat: no-repeat;
    margin-right: 12px;
  }
}

.customer-area-sidebar__li.active {
  .customer-sidebar-link::before {
    filter: invert(1);
  }
}

.orders-list::before {
  background-image: url('../../../images/icons/orders-list.svg');
}

.catering::before {
  background-image: url('../../../images/icons/catering-black.svg');
}

.snacks::before {
  background-image: url('../../../images/icons/snacks-black.svg');
}

.meal-plans::before {
  background-image: url('../../../images/icons/meal-plans.svg');
}


.team-orders-list::before {
  background-image: url('../../../images/icons/team.svg');
}

.quotes-list::before {
  background-image: url('../../../images/icons/quote.svg');
}

.with-favourite-heart::before {
  background-image: url('../../../images/icons/heart-empty.svg');
}

.billing-list::before {
  background-image: url('../../../images/icons/billing.svg');
}

.reports-list::before {
  background-image: url('../../../images/icons/chart.svg');
}

.surveys-list::before {
  background-image: url('../../../images/icons/survey.svg');
}

.settings-list::before {
  background-image: url('../../../images/icons/settings.svg');
}

.personalised {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  &__tag.circle-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #dc2454;
    color: white;
    font-size: 14px;
    width: 36px;
    height: 36px;
  }
  &__name {
    display: block;
    font-size: 14px;
    line-height: 1.2rem;
    color: $black;
    font-family: $body-font;
    margin: 0;
    &.sidebar {
      max-width: 150px;
    }
    &--header {
      margin-left: -18px;
      @include media-down(hamburger) {
        display: none !important;
      }
    }
    &:hover,
    &:focus {
      color: $primary;
    }
    &::after {
      border-color: black transparent transparent !important;
    }
  }
  &__company {
    display: block;
    font-size: 14px;
    color: black;
    line-height: 1.2rem;
  }
}

.customer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem !important;
  &__info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-right: 1.5rem;
  }
  &__title {
    color: $black;
    font-weight: bold;
    font-family: $body-font;
    margin: 0;
  }
  &__auth {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .personalised__tag {
      @include media-down(small-tablet) {
        display: none;
      }
    }
  }
  &__create-new {
    @include media-down(small-tablet) {
      display: none;
    }
    span {
      display: flex;
      align-items: center;
      background: white;
      color: black;
      padding: 8px 21px;
      border-radius: 4px;
      font-size: 13px;
      font-weight: bold;
      text-align: center;
      border: 1px solid #d4d4d4;
      margin-left: 20px;
      cursor: pointer;
      &::after {
        content: "";
        display: inline-block;
        background: url("../../../images/icons/plus-block.svg") no-repeat;
        width: 14px;
        height: 14px;
        margin-left: 10px;
      }
    }
  }
  &__dropdown {
    color: black;
    background: white;
    margin-top: 9px;
    width: 230px;
    a {
      color: black;
      margin: 0 16px;
      border-radius: 4px;
    }
    a:hover {
      cursor: pointer;
      background: #e8edf2;
    }
    .show-all-linked {
      color: $primary;
      font-weight: bold;
      &:hover {
        color: darken($primary, 5%);
      }
    }
    &--linked {
      min-width: 130px;
      width: 100%;
      margin-top: 7px;
      &::before {
        content: none;
      }
      a {
        margin: 0 4px;
      }
    }
  }
}

.dash-header {
  position: sticky;
  top: 0;
  z-index: 999;
  display: grid;
  grid-column-gap: 12px;
  align-items: center;
  background: white;
  padding: 8px 24px;
  padding-left: 8px;
  border-bottom: 1px solid #eee;
  border-left: 1px solid #eee;
  font-size: 14px;
  min-height: 55px;
  &.suppliers-dash {
    grid-template-columns: 4fr 5fr 1fr 1fr 2fr;
    @include media-down(large-tablet) {
      grid-template-columns: 4fr 1fr 1fr 1fr;
    }
  }
  &.menu-dash {
    grid-template-columns: 4fr 5fr 1fr 1fr;
  }
  &.checkout-dash, &.quotes-dash, &.generic-dash {
    display: grid;
    grid-template-columns: 10fr 1fr 2fr;
  }
  > div {
    border-right: 1px solid rgb(204, 204, 204);
    padding-right: 12px;
    height: 100%;
    &:last-of-type {
      border: none;
    }
  }
  > a {
    border-right: 1px solid rgb(204, 204, 204);
    padding-right: 12px;
    height: 100%;
  }
}

.dash-name {
  @include media-down(large-tablet) {
    display: none;
  }
}

.dash-cart {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  &::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    background-image: url('../../../images/icons/shopping-cart-dark.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    margin-left: 8px;
  }
  .dash-cart-count {
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    background: $highlight;
    border-radius: 50%;
    width: 20px;
    height: 20px;
  }
}

.listing-filters, .sidebar-mobile {
  display: flex;
  align-items: center;
  justify-content: center;
}

.listing-filters {
  @include media-down(large-tablet) {
    display: none;
  }
}

.sidebar-mobile {
  @include media-up(large-tablet) {
    display: none;
  }
}

.dash-user {
  position: relative;
  display: flex;
  align-items: center;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  overflow: visible;
  cursor: pointer;
}

.checkout-dash, .quotes-dash {
  display: flex;
}

.auth-dropdown-tooltip {
  position: absolute;
  top: 40px;
  right: 0;
  border-radius: 4px;
  background: white;
  color: black;
  background: white;
  margin-top: 9px;
  width: 230px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 1rem 0;
}

.auth-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
  line-height: 16px;
}

.invite-team {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: rgb(100, 100, 100);
  &:hover {
    text-decoration: underline;
  }
  &::before {
    content: '';
    display: inline-block;
    background-image: url('../../../images/icons/users.svg');
    background-repeat: no-repeat;
    width: 20px;
    height: 20px;
    background-size: contain;
    background-position: center;
    margin-right: 8px;
  }
}

.company-name {
  margin-bottom: 8px;
  font-size: 16px;
}

.auth-admin {
  font-size: 16px;
  margin-bottom: 8px;
  margin-top: 12px;
  display:flex;
  align-items: center;
  &::before {
    content: '';
    display: inline-block;
    width: 18px;
    height: 18px;
    background-image: url('../../../images/icons/user.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 4px;
  }
}

.nested-link {
  &::after {
    content: '';
    display: inline-block;
    margin-left: auto;
    background: url('../../../images/icons/chevron-thick.svg');
    transform: rotate(-90deg);
    background-repeat: no-repeat;
    background-size: contain;
    width: 14px;
    height: 14px;
    transition: transform .1s ease;
  }
  &.open::after {
    transform: rotate(0deg)
  }
}

.customer-nested-link {
  color: #191919;
  opacity: 0.5;
  font-size: 14px;
  transition: background-color 0.25s ease-out, color 0.25s ease-out;
  background: none;
  border-radius: 10px;
  margin: 0 1rem;
  margin-left: 22px;
  display: block;
  padding: 0.7rem 1rem;
  line-height: 1;
  &:hover {
    color: black;
    text-decoration: none;
    opacity: 1;
  }
}

.auth-dropdown {
  flex: 1;
  margin: 0 16px;
  border-radius: 4px;
  color: #241c15;
  padding: 8px 12px;
  cursor: pointer;
  &:hover {
    background: #e8edf2;
  }
}

.dash-container {
  flex: 1;
  max-width: calc(100% - 250px);
  @include media-down(large-tablet) {
    max-width: initial;
  }
}

.dash-heading {
  font-size: 1.125rem;
  font-weight: bold;
  font-family: $body-font;
  margin-right: auto;
  padding-left: 12px;
}

.cart-container {
  display: flex;
  align-items: center;
  justify-content: center;
  .clear-cart {
    font-size: 20px;
    margin-left: 10px;
  }
}