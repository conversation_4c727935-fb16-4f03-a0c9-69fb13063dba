import { useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Modal from 'react-responsive-modal';
import NProgress from 'nextjs-progressbar';

import { UserContext } from 'context/user';
import { HostContext } from 'context/host';
import useSidebarStore from 'store/useSidebarStore';
import { FiltersModal, GeneralSearch } from 'components/SupplierIndex';
import { AuthDropdown, DashLocationBar, Hamburger, Link } from 'components/Common';
import { MajorSearchMenuItems, SearchMenuItems } from 'components/SupplierShow';
import { DYNAMIC_ORDER_EDIT_PAGE, CHECKOUT_PAGE } from 'api/endpoints';

import useDeliveryDateStore from 'store/useDeliveryDateStore';
import useDocketStore from 'store/useDocketStore';
import useCheckoutStore from 'store/useCheckoutStore';

const DashHeader = ({ categories, type }) => {
  const { user, cart: sessionCart } = useContext(UserContext);
  const { appURL, marketingURL } = useContext(HostContext);
  const { clearOrder, isWoolworthsOrder, fetchedSession, zustandCartCount, docketOrderID, docketStatus } = useDocketStore((state) => ({
    clearOrder: state.clearOrder,
    fetchedSession: state.fetchedSession,
    isWoolworthsOrder: state.order?.isWoolworthsOrder,
    zustandCartCount: state.orders[state?.activeOrderID]?.totals?.order_line_count,
    docketOrderID: state.order?.id,
    docketStatus: state.order?.status,
  }));

  const { checkoutOrderID, checkoutStatus } = useCheckoutStore((state) => ({
    checkoutOrderID: state.order?.id,
    checkoutStatus: state.order?.status,
  }));

  const {
    query: { supplier, mealUUID },
  } = useRouter();
  const { toggleSidebar } = useSidebarStore();

  const [modalOpen, setModalOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [cartCount, setCartCount] = useState(sessionCart?.count);
  let closeDropdownTimeout;

  const [showClearCartModal, setShowClearCartModal] = useState(false);
  const clearDate = useDeliveryDateStore((state) => state.clearDate);
  const orderID = checkoutOrderID || docketOrderID;
  const status = checkoutStatus || docketStatus;
  const cartLink = (orderID && status && status !== 'draft') ? DYNAMIC_ORDER_EDIT_PAGE(orderID) : CHECKOUT_PAGE;

  async function handleClearCart() {
    try {
      await clearOrder();
      clearDate();
      setShowClearCartModal(false);
      setCartCount(0);
      if (type === 'checkout-dash') {
        let redirectLink;
        if (user) {
          redirectLink = `${appURL}/c_profile`;
          if (sessionCart.mealUUID) {
            redirectLink += `/meal-plans?mealUUID=${sessionCart.mealUUID}`;
          }
        } else {
          redirectLink = marketingURL;
        }
        window.location = redirectLink;
      }
    } catch (err) {
      console.log(err);
    }
  }

  useEffect(() => {
    if (zustandCartCount) {
      setCartCount(zustandCartCount);
    }
    if (fetchedSession && !zustandCartCount) {
      setCartCount(0);
    }
  }, [zustandCartCount]);

  const handleMouseEnter = () => {
    clearTimeout(closeDropdownTimeout); // Clear any pending close action
    setIsDropdownOpen(true);
  };

  const handleMouseLeave = () => {
    // Set a delay to close the dropdown
    closeDropdownTimeout = setTimeout(() => {
      setIsDropdownOpen(false);
    }, 300); // 300ms delay
  };

  const isMajorSupplier = supplier === 'woolworths';

  return (
    <div className={`dash-header ${type || 'generic-dash'}`}>
      <NProgress color="#000" options={{ showSpinner: false }} />
      {!type && <h3 className="dash-heading" />}
      {type === 'checkout-dash' && <h3 className="dash-heading">Checkout</h3>}
      {type === 'quotes-dash' && <h3 className="dash-heading">Quotes</h3>}
      {type === 'menu-dash' && (
        <>
          <DashLocationBar disabled />
          {!isMajorSupplier && <SearchMenuItems forDash />}
          {isMajorSupplier && <MajorSearchMenuItems forDash />}
        </>
      )}
      {type === 'suppliers-dash' && (
        <>
          <DashLocationBar disabled={!!mealUUID} />
          <GeneralSearch categoryPage="office-catering" forDash />
          <div className="listing-filters" onClick={() => setModalOpen(true)}>
            <Hamburger show forDash />
            <span>Filters</span>
          </div>
        </>
      )}
      <div className="cart-container">
        <Link href={cartLink} className="dash-cart">
          {!!cartCount && type !== 'checkout-dash' && <span className="dash-cart-count">{cartCount}</span>}
        </Link>
        {(!!cartCount || isWoolworthsOrder) && (
          <a className="clear-cart" onClick={() => setShowClearCartModal(true)}>
            x
          </a>
        )}
        <Modal
          open={showClearCartModal}
          center
          styles={{
            modal: { maxWidth: '500px', padding: '20px', borderRadius: '10px' },
            closeButton: { cursor: 'pointer', marginLeft: '6px' },
          }}
          showCloseIcon={false}
          onClose={() => {}}
        >
          <p className="clear-cart-confirmation">Are you sure you want to clear the cart?</p>
          <div className="clear-cart-btns">
            <button type="button" className="button black clear-cart" onClick={() => setShowClearCartModal(false)}>
              No
            </button>
            <button type="button" className="button clear-cart" onClick={handleClearCart}>
              Yes
            </button>
          </div>
        </Modal>
      </div>
      <div className="sidebar-mobile">
        <Hamburger show forDash onClick={() => toggleSidebar()} />
      </div>
      <div className="dash-user" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
        <span className="circle-icon dash" style={{ backgroundColor: '#dc2454', color: 'white' }}>
          {user.first_name[0]}
          {user.last_name[0]}
        </span>
        <span className="dash-name">
          {user.first_name} {user.last_name}
        </span>
        {isDropdownOpen && <AuthDropdown user={user} setIsDropdownOpen={setIsDropdownOpen} />}
      </div>
      <Modal
        open={modalOpen}
        center
        styles={{
          modal: { padding: '8px 20px', borderRadius: '10px', width: '460px', height: '500px', overflow: 'scroll' },
          closeButton: { cursor: 'pointer', marginLeft: '6px' },
        }}
        showCloseIcon
        onClose={() => setModalOpen(false)}
      >
        <FiltersModal categories={categories} />
      </Modal>
    </div>
  );
};

export default DashHeader;
