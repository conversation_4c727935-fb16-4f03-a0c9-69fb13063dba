@import 'SupplierDetails';
@import 'SupplierFilters';
@import 'SupplierGrid';
@import 'SupplierGridSkeleton';

.search-results {
  padding-top: 10px;
  &.dash {
    padding-top: 0;
    .search-results-header {
      padding-top: 30px;
    }
  }
  .wrapper {
    @include media-down(large-tablet) {
      padding: 0 10px;
    }
  }

  .search-results-header {
    padding: 20px 0 4px 20px;
    @include media-down(large-tablet) {
      flex-direction: column;
      text-align: center;
    }
    .search-results-title {
      font-size: 26px;
      @include media-down(large-tablet) {
        margin-bottom: 20px;
        font-size: 22px;
      }
      > span {
        background: $off-black;
        color: $white;
        display: inline-block;
        padding: 8px 10px;
        margin: 0 4px;
        @include media-down(large-tablet) {
          margin: 0;
        }
      }
    }
  }

  .search-results-search-bar {
    position: relative;
    .icon {
      position: absolute;
      top: 12px;
      right: 13px;
      z-index: 3;
      &:before {
        width: 20px;
        height: 20px;
      }
    }
  }

  .search-results-filters {
    display: flex;
    padding-top: 10px;
    &.loading {
      padding-bottom: 50px;
    }
    .search-results-search-bar {
      width: 33.33%;
      margin-right: 10px;
      @include media-down(large-tablet) {
        width: 100%;
        margin: 0;
      }
      input {
        padding: 14px;
        @include media-down(small-mobile) {
          &::placeholder {
            font-size: 14px;
          }
        }
      }
    }

    .search-results-dropdown {
      margin-right: 10px;
      width: 17%;
      .dropdown {
        white-space: nowrap;
      }
      &.loading {
        @include shimmer;
      }
      .dropdown-item {
        padding: 0 4px;
        margin-bottom: 6px;
          &:hover, &.active {
              cursor: pointer;
              background: black;
              color: white;
              border-radius: 4px;
            }
      }
      @include media-down(large-tablet) {
        display: none;
      }
    }
  }
}
.filter-tag-row {
  display:flex;
  align-items: center;
  margin-bottom: 20px;
  span {
    margin-right: 10px;
  }
  .filter-tag {
    display: inline-block;
    cursor: pointer;
    background: $off-black;
    padding: 4px 7px;
    color: white;
    margin-top: 4px;
    margin-right: 10px;
    &::after {
      content: '𝘅';
      display: inline-block;
      margin-left: 8px;
    }
  }
  > ul > span {
    @include media-down(large-tablet) {
      display: none;
    }
  }
}

.supplier-listing-container {
  display: grid;
  grid-template-columns: 1fr 5fr;
  @include media-down(large-tablet) {
    display: block;
  }
}

.suppliers-list {
    background: #FDFDFD;
    margin-right: -60px;
    padding-right: 40px;
    padding-left: 20px;
    padding-top: 20px;
    padding-bottom: 50px;
    @include media-down(large-tablet) {
      margin: 0;
      padding: 0;
    }
}

.supplier-index {
  display: flex;
}

.supplier-show {
  display: flex;
  .supplier-show-wrapper{
    padding-left: 0;
    padding-right: 20px;
  }
  .supplier-menu-banner {
    padding-left: 16px;
  }
  .supplier-banner {
    padding-left: 16px;
  }
  .menu-sections .menu-section {
    padding-left: 16px;
  }
}

.meal-filters {
  display: flex;
  align-items: center;
  padding-left: 20px;
  margin-bottom: 12px;
  .filter-title {
    font-size: 18px;
  }
  .meal-filter {
    padding: 4px 12px;
    border: 1px solid gray;
    margin-left: 12px;
    border-radius: 4px;
    cursor: pointer;
    &:hover {
      background: black;
      color: white;
    }
    &.active {
      background: black;
      color: white;
    }
  }
}

