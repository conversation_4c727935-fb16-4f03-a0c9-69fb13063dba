import useDeliveryDateStore from 'store/useDeliveryDateStore';
import useFilterStore from 'store/useFilterStore';

const FilterTags = ({ filters }) => {
  const toggleFilter = useFilterStore((state) => state.toggleFilter);
  const dateFilterRegex = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
  const isDateFilter = (filter) => dateFilterRegex.test(filter);
  const { clearDate } = useDeliveryDateStore((state) => state);

  return (
    <div className="filter-tag-row">
      <ul>
        <span>Filtering Results By:</span>
        {filters.map((filterTag) => (
          <li
            key={filterTag}
            className="filter-tag"
            onClick={() => {
              if (isDateFilter(filterTag)) {
                clearDate();
              }
              toggleFilter(filterTag);
            }}
            role="presentation"
          >
            {filterTag}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FilterTags;
